
// Define the tool types
export enum ETool {
    PriceRange = 'priceRange',
    Ray = 'ray',
    Note = 'note',
    None = '',
    TimeRange = 'timeRange',
    RangeTradeDist = 'rangeTradeDist',
    HorizontalRayChannel = 'horizontalRayChannel',
    ProgressiveTradeDist = 'progressiveTradeDist',
    RayChannel = 'rayChannel',
    Rectangle = 'rectangle'
}

export type TDrawingConfig = {
    id?: string;
    symbol: string;
    timeframe: string;
    chart: string;
    type: ETool | string;
    dataPoints: { time: number, value: number }[];
}

export const EToolControlDotCount = {
    [ETool.PriceRange]: 2,
    [ETool.Ray]: 2,
    [ETool.Note]: 1,
    [ETool.None]: 0,
    [ETool.TimeRange]: 2,
    [ETool.RangeTradeDist]: 2,
    [ETool.HorizontalRayChannel]: 2,
    [ETool.ProgressiveTradeDist]: 2,
    [ETool.RayChannel]: 4,
    [ETool.Rectangle]: 2,
}


