import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TAlertTriggerType } from "../types/alertTypes";
import type { TDataPoint } from "../types/plotTypes";
import type { TOrderData, TPosition } from "../types/tradingTypes";

const API_HOST = 'http://localhost:5090';
const ANALYTICS_HOST = 'http://localhost:6090';

interface FetchOptions extends RequestInit {
    params?: Record<string, any>;
    enpotint?: string;
}

async function apiFetch<T>(path: string, options: FetchOptions = {}): Promise<T> {
    const { params, ...fetchOptions } = options;

    // Build URL with query parameters
    let url = `${options.enpotint ?? API_HOST}${path}`;
    if (params) {
        const query = new URLSearchParams(
            Object.entries(params)
                .filter(([_, value]) => value != null)
                .map(([key, value]) => [key, String(value)])
        ).toString();
        if (query) url += `?${query}`;
    }

    // Set default headers
    const headers = new Headers(fetchOptions.headers);
    if (fetchOptions.body && !headers.has('Content-Type')) {
        headers.set('Content-Type', 'application/json');
    }


    const response = await fetch(url, {
        ...fetchOptions,
        headers,
    });

    if (!response.ok) {
        const error = new Error(`Api Error: `);
        const message = await response.text();
        error.message += message
        throw error;
    }

    return response.json();
}

// API Functions grouped by domain
export function fetchSymbolList() {
    return apiFetch<string[]>('/symbol');
}

export function fetchTimeframeList() {
    return apiFetch<string[]>('/timeframe');
}

export function createDrawingConfig(params: {
    symbol: string;
    timeframe: string;
    chart: string;
    type: string;
    dataPoints: TDataPoint[];
}) {
    return apiFetch<any>('/drawing-config', {
        method: 'POST',
        body: JSON.stringify(params),
    });
}

export function fetchDrawingConfigs(params: { symbol: string; chart?: string }) {
    return apiFetch<any>('/drawing-config', { params });
}

export function updateDrawingConfig(id: string, dataPoints: TDataPoint[]) {
    return apiFetch<any>(`/drawing-config/${id}`, {
        method: 'PUT',
        body: JSON.stringify({ dataPoints }),
    });
}

export function deleteDrawingConfig(id: string) {
    return apiFetch<any>(`/drawing-config/${id}`, {
        method: 'DELETE',
        body: JSON.stringify({}),
    });
}

export function clearDrawingConfigs(symbol: string) {
    return apiFetch<any>('/drawing-config', {
        method: 'DELETE',
        params: { symbol },
    });
}

export function fetchTradeDist(params: {
    symbol: string;
    timeframe: string;
    startTime: number;
    endTime: number;
}) {
    return apiFetch<any>('/trade-dist', { params, enpotint: ANALYTICS_HOST });
}

export function fetchProgressiveTradeDist(params: {
    symbol: string;
    timeframe: string;
    startTime: number;
    endTime: number;
}) {
    return apiFetch<any>('/progressive-trade-dist', { params, enpotint: ANALYTICS_HOST });
}

export function fetchAlertTriggerTypeList() {
    return apiFetch<TAlertTriggerType[]>('/alert-trigger-type');
}

export function fetchAlertOpList() {
    return apiFetch<TAlertOp[]>('/alert-op');
}

export function createAlertConfig(params: TAlertConfigNew) {
    return apiFetch<any>('/alert-config', {
        method: 'POST',
        body: JSON.stringify(params),
    });
}

export function fetchAlertConfigs() {
    return apiFetch<any>('/alert-config');
}

export function deleteAlertConfig(id: string) {
    return apiFetch<any>(`/alert-config/${id}`, {
        method: 'DELETE',
    });
}

export function placeMarketOrder(params: {
    symbol: string;
    side: string;
    usdAmount: number;
}): Promise<{ orderId: string; cryptoAmount: string; symbol: string; side: string }> {
    return apiFetch('/order', {
        method: 'POST',
        body: JSON.stringify(params),
    });
}

export function fetchOrders(symbol: string): Promise<TOrderData[]> {
    return apiFetch('/order', { params: { symbol } });
}

export function fetchPositionList(): Promise<{
    symbol: string;
    side: string;
    cryptoAmount: string;
    usdAmount: string;
}[]> {
    return apiFetch('/position');
}

export function fetchAccount(): Promise<{
    availableMargin: string;
    leverage: number;
    positions: TPosition[];
}> {
    // return Promise.resolve({
    //     availableMargin: '1000',
    //     leverage: 20,
    //     positions: []
    // })
    return apiFetch('/account');
}

export function closePosition(symbol: string): Promise<{
    orderId: string;
    symbol: string;
    side: string;
}> {
    return apiFetch('/position/close', {
        method: 'POST',
        body: JSON.stringify({ symbol }),
    });
}

export function savePushSubscription(subscription: any) {
    return apiFetch<any>('/push-subscription', {
        method: 'POST',
        body: JSON.stringify(subscription),
    });
}

export function fetchChartDataList(params: {
    symbol: string;
    timeframe: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
}) {
    return apiFetch<any>('/chart-data', { params });
}