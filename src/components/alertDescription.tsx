import { JSX } from "react";
import { getFormatNumStr } from "../commons/util";
import {
    AlertDescriptionContainer,
    AlertDrawingDescription,
    AlertDrawingContent,
    AlertIconWrapper,
    AlertGenericIcon
} from "../styles/alertStyles";
import RayIcon from "../icons/ray.icon";
import RayChannelIcon from "../icons/rayChannel.icon";
import PriceRangeIcon from "../icons/priceRange.icon";
import HorizontalRayChannelIcon from "../icons/horizontalRayChannel.icon";
import TimeRangeIcon from "../icons/timeRange.icon";
import ProgressiveTradeDist from "../icons/progressiveTradeDist.icon";
import RangeTradeDist from "../icons/rangeTradeDist.icon";
import RectangleIcon from "../icons/rectangle.icon";
import { TAlertConfig } from "../types/alertTypes";


const getDrawingIcon = (drawingName: string): JSX.Element => {
    const iconProps = { size: 24 };

    switch (drawingName?.toLowerCase()) {
        case 'ray':
            return <AlertIconWrapper><RayIcon {...iconProps} /></AlertIconWrapper>;
        case 'raychannel':
        case 'ray channel':
            return <AlertIconWrapper><RayChannelIcon {...iconProps} /></AlertIconWrapper>;
        case 'pricerange':
        case 'price range':
            return <AlertIconWrapper><PriceRangeIcon {...iconProps} /></AlertIconWrapper>;
        case 'horizontalraychannel':
        case 'horizontal ray channel':
            return <AlertIconWrapper><HorizontalRayChannelIcon {...iconProps} /></AlertIconWrapper>;
        case 'timerange':
        case 'time range':
            return <AlertIconWrapper><TimeRangeIcon {...iconProps} /></AlertIconWrapper>;
        case 'progressivetradedist':
        case 'progressive trade dist':
            return <AlertIconWrapper><ProgressiveTradeDist {...iconProps} /></AlertIconWrapper>;
        case 'rangetradedist':
        case 'range trade dist':
            return <AlertIconWrapper><RangeTradeDist {...iconProps} /></AlertIconWrapper>;
        case 'rectangle':
            return <AlertIconWrapper><RectangleIcon {...iconProps} /></AlertIconWrapper>;
        default:
            // Return a generic drawing icon or the first letter
            return <AlertGenericIcon>{drawingName?.[0]?.toUpperCase() || 'D'}</AlertGenericIcon>;
    }
};



export const AlertDescription = ({ alert }: { alert: TAlertConfig }): JSX.Element => {
    const opText = alert.op ? `${alert.op.name} ` : '';
    const symbolText = alert.symbol?.replace('usdt', '').toUpperCase();
    const timeframeText = alert.timeframe;
    const alertId = alert.id.toString().substring(0, 6);

    // For price alerts
    if (alert.arg2?.type === 'value') {
        return (
            <AlertDescriptionContainer>
                {symbolText}-{timeframeText} • {opText} {getFormatNumStr(alert.arg2.value!)} #{alertId}
            </AlertDescriptionContainer>
        );
    }
    // For drawing alerts
    else if (alert.arg2?.type === 'drawing') {
        return (
            <AlertDrawingDescription>
                <span>{symbolText}-{timeframeText}</span>
                <AlertDrawingContent>
                    • {opText}{getDrawingIcon(alert.arg2.name)} #{alertId}
                </AlertDrawingContent>
            </AlertDrawingDescription>
        );
    }
    // Generic fallback
    else {
        return (
            <AlertDescriptionContainer>
                {symbolText}-{timeframeText} • {opText}Alert #{alertId}
            </AlertDescriptionContainer>
        );
    }
};
