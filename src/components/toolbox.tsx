import { useState } from "react";
import { ClearDrawingsButton, ToolboxMenuContainer, ToolboxItem, ToolboxTooltip, ToolboxContainer } from "../styles/toolboxStyles";
import { useAppContext, useDataContext, useNotificationContext } from "../contexts/contexts";
import { ETool } from "../types/toolboxTypes";
import HorizontalRayChannelIcon from "../icons/horizontalRayChannel.icon";
import PriceRangeIcon from "../icons/priceRange.icon";
import RangeTradeDistIcon from "../icons/rangeTradeDist.icon";
import RayIcon from "../icons/ray.icon";
import RayChannelIcon from "../icons/rayChannel.icon";
import TrashIcon from "../icons/trash.icon";
import { clearDrawingConfigs } from "../commons/api";
import ProgressiveTradeDistIcon from "../icons/progressiveTradeDist.icon";
import TimeRangeIcon from "../icons/timeRange.icon";
import RectangleIcon from "../icons/rectangle.icon";


export const Toolbox = () => {
    const [hoveredTool, setHoveredTool] = useState<ETool | null>(null);
    const { activeTool, symbol } = useAppContext()
    const { drawingConfigList } = useDataContext()
    const { success, error } = useNotificationContext()
    activeTool.use()
    // Define the tools with their icons and tooltips
    const tools = [
        { id: ETool.Ray, icon: RayIcon, tooltip: 'Ray' },
        { id: ETool.RayChannel, icon: RayChannelIcon, tooltip: 'Ray Channel' },
        { id: ETool.HorizontalRayChannel, icon: HorizontalRayChannelIcon, tooltip: 'Horizontal Ray Channel' },
        { id: ETool.PriceRange, icon: PriceRangeIcon, tooltip: 'Price Range' },
        { id: ETool.RangeTradeDist, icon: RangeTradeDistIcon, tooltip: 'Range Trade Distribution' },
        { id: ETool.ProgressiveTradeDist, icon: ProgressiveTradeDistIcon, tooltip: 'Progressive Trade Distribution' },
        { id: ETool.TimeRange, icon: TimeRangeIcon, tooltip: 'Time Range' },
        { id: ETool.Rectangle, icon: RectangleIcon, tooltip: 'Rectangle' },

        // Add more tools here as needed
    ];

    const handleToolClick = (toolId: ETool) => {
        if (activeTool.value === toolId) {
            activeTool.value = ETool.None
        } else {
            activeTool.value = toolId
        }
    };

    return (
        <ToolboxContainer>
            <ToolboxMenuContainer>
                {tools.map((tool) => {
                    const isActive = activeTool.value === tool.id;
                    const isHovered = hoveredTool === tool.id;

                    return (
                        <div
                            key={tool.id}
                            style={{ position: 'relative' }}
                            onMouseEnter={() => setHoveredTool(tool.id)}
                            onMouseLeave={() => setHoveredTool(null)}
                        >
                            <ToolboxItem
                                active={isActive}
                                onClick={() => handleToolClick(tool.id)}
                            >
                                <tool.icon color="rgb(7, 81, 207)" size={30} />
                            </ToolboxItem>

                            <ToolboxTooltip
                                style={{
                                    opacity: isHovered ? 1 : 0,
                                    transform: isHovered ? 'translateY(-50%)' : 'translateY(-50%) translateX(-10px)',
                                }}
                            >
                                {tool.tooltip}
                            </ToolboxTooltip>
                        </div>
                    );
                })}
            </ToolboxMenuContainer>

            <ClearDrawingsButton type='danger' onClick={async () => {
                await clearDrawingConfigs(symbol)
                    .then(() => {
                        success({ title: 'Drawings cleared', message: '' });
                    })
                    .catch((e) => {
                        error({ title: 'Failed to clear drawings', message: e.message });
                    })
                drawingConfigList.value = []
            }}>
                <TrashIcon size={20} color='red' />
            </ClearDrawingsButton>
        </ToolboxContainer>

    );
};
